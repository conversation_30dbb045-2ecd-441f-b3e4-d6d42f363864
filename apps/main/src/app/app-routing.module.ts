import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { DefaultLayoutComponent } from '../components/bussiness-components/layout/default-layout/default-layout.component';
import { AdAuthGuardService } from '../pages/login/service/auth-guard.service';
import { GlobalService } from '../common/service/global.service';
import { AppInfoGuard } from '../pages/app/app.guard';
import { SelectedAppResolver } from '../pages/app/selected-app-resolver.resolver';
import { AppTypes, ExtensionAppTypes } from '../pages/app/typings';
import { ApiInquireComponent } from '../pages/api-tool/api-inquire/api-inquire.component';
import { AuthGuard } from 'common/guards/auth.guard';
import { AuthResources } from 'common/types/auth.types';
import { TenantToAppsGuard } from 'common/guards/tenant-to-apps.guard';
import { DevOpsCenterLayoutComponent } from 'pages/devOps-center/devOps-center-layout/devOps-center-layout.component';
import { StandaloneTopMenuResolver } from 'pages/standalone-solution/resolver/standalone-top-menu.resolve';
import { IndividualGuard } from 'pages/individual/individual.guard';
import { NoticeGuard } from 'common/guards/notice.guard';
import { IndividualLoginGuard } from 'pages/individual/individual-login.guard';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'prefix',
    component: DefaultLayoutComponent,
    canActivate: [NoticeGuard, AuthGuard],
    canActivateChild: [NoticeGuard],
    data: { authPrefix: AuthResources.TENANT },
    children: [
      {
        path: '',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/home/<USER>').then((m) => m.HomeModule),
        canActivateChild: [AdAuthGuardService],
        canActivate: [TenantToAppsGuard],
      },
      {
        path: 'apps',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/apps/apps.module').then((m) => m.AppsModule),
        canActivateChild: [AdAuthGuardService],
      },
      // 个案设计器
      {
        path: 'individual',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/individual/individual.module').then((m) => m.IndividualModule),
        canActivateChild: [AdAuthGuardService, IndividualGuard],
      },
      {
        // 独立解决方案
        path: 'standalone-solution',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../pages/standalone-solution/standalone-solution.module').then((m) => m.StandaloneSolutionModule),
        resolve: {
          selectedApp: SelectedAppResolver,
          solutionTopMenus: StandaloneTopMenuResolver,
        },
        data: { authPrefix: AuthResources.APPLICATION },
      },
      {
        // 独立解决方案-has路由
        path: 'standalone-solution#',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../pages/standalone-solution/standalone-solution.module').then((m) => m.StandaloneSolutionModule),
        resolve: {
          selectedApp: SelectedAppResolver,
          solutionTopMenus: StandaloneTopMenuResolver,
        },
        data: { authPrefix: AuthResources.APPLICATION },
      },
      {
        path: 'app',
        pathMatch: 'prefix',
        loadChildren: (): any => {
          return import('../pages/pure-designer/pure-designer.module').then((m) => m.PureDesignerModule);

          const appType = GlobalService.appType;
          // 本身就是高代码应用
          // 其他应用+高代码应用
          // 这个要写在最上面
          if (
            appType === AppTypes.HIGH_CODE ||
            location.search.includes(`&extensionApp=${ExtensionAppTypes.HIGHTCODE}`)
          ) {
            return import('../pages/app-hight-code/app-height-code.module').then((m) => m.AppHeightCodeModule);
          }

          if (appType === AppTypes.DTD || appType === AppTypes.COLLECT_DATA) {
            return import('../pages/app/app.module').then((m) => m.AppModule);
          }
          // if (appType === AppTypes.COLLECT_DATA_V2) {
          //   // 数据收集型解决方案
          //   return import('./app-collection/app-collection.module').then((m) => m.AppCollectionModule);
          // }
          if (appType === AppTypes.SCENARIO_KIT) {
            // 场景化套件解决方案
            return import('../pages/app-kit/app-kit.module').then((m) => m.AppKitModule);
          }
          if (appType === AppTypes.MODEL_DRIVEN) {
            // 模型驱动类型解决方案
            return import('../pages/app-model-driven/app-model-driven.module').then((m) => m.AppModelDrivenModule);
          }
          if (appType === AppTypes.AGILE_DATA) {
            // 敏捷数据类型解决方案
            //return import('../pages/app-agile-questions/app-agile-questions.module').then((m) => m.AppAgileQuestionsModule);
            return import('../pages/app-agile-data/app-agile-data.module').then((m) => m.AppAgileDataModule);
          }
          if (appType === AppTypes.AI_MODEL) {
            // ai解决方案
            return import('../pages/app-ai-model/app-ai-model.module').then((m) => m.AppAiModelModule);
          }
          if (appType === AppTypes.BUSINESS_DOMAIN_CENTER) {
            // 业务领域中心解决方案
            return import('../pages/business-domain-center/business-domain-center.modules').then(
              (m) => m.BusinessDomainCenterModule,
            );
          }
          if (appType === AppTypes.AGILE_QUESTIONS) {
            // 敏捷问数类型解决方案
            return import('../pages/app-agile-questions/app-agile-questions.module').then(
              (m) => m.AppAgileQuestionsModule,
            );
          }
        },
        canLoad: [AdAuthGuardService, AppInfoGuard],
        canActivate: [AdAuthGuardService, AuthGuard],
        resolve: {
          selectedApp: SelectedAppResolver,
        },
        data: { authPrefix: AuthResources.APPLICATION },
      },
      // {
      //   path: 'data-standard',
      //   pathMatch: 'prefix',
      //   loadChildren: (): any =>
      //     import('../pages/data-standard/data-standard.module').then((m) => m.DataStandardModule),
      //   canActivateChild: [AdAuthGuardService],
      // },
      // /* 微前端data-standard入口 */
      // {
      //   path: 'data-standard2',
      //   redirectTo: 'athena-designer-core/data-standard',
      // },
      {
        path: 'asset-center',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/asset-center/asset-center.module').then((m) => m.AssetCenterModule),
        canActivateChild: [AdAuthGuardService],
        canActivate: [AuthGuard],
        data: { authPrefix: AuthResources.RESULT },
      },
      {
        path: 'template-center',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../pages/template-center/template-center.module').then((m) => m.TemplateCenterModule),
        canActivateChild: [AdAuthGuardService],
        canActivate: [AuthGuard],
        data: { authPrefix: AuthResources.RESULT },
      },
      {
        path: 'data-center',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/data-center/data-center.module').then((m) => m.DataCenterModule),
        canActivateChild: [AdAuthGuardService],
        canActivate: [AuthGuard],
        data: { authPrefix: AuthResources.DATA_STANDARD },
      },
      {
        path: 'athena-designer-editor',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../pages/micro-athena-designer-editor/micro-athena-designer-editor.module').then(
            (m) => m.MicroDataStandardModule,
          ),
        canActivateChild: [AdAuthGuardService],
      },
      // {
      //   path: 'deployer',
      //   pathMatch: 'prefix',
      //   loadChildren: (): any => import('../pages/deployer/deployer.module').then((m) => m.DeployerModule),
      //   canActivateChild: [AdAuthGuardService],
      // },
      /* 微前端deployer入口 */
      // {
      //   path: 'deployer',
      //   redirectTo: 'athena-designer-core/deployer',
      // },
      // {
      //   path: 'deployer',
      //   pathMatch: 'prefix',
      //   loadChildren: (): any => import('../pages/deploy/deploy.module').then((m) => m.DeployModule),
      //   canActivateChild: [AdAuthGuardService],
      //   canActivate: [AuthGuard],
      //   data: { authPrefix: AuthResources.PUBLISH },
      // },
      {
        path: '',
        pathMatch: 'prefix',
        component: DevOpsCenterLayoutComponent,
        children: [
          {
            path: 'devOps-center',
            pathMatch: 'prefix',
            loadChildren: (): any =>
              import('../pages/devOps-center/devOps-center.module').then((m) => m.DevOpsCenterModule),
            canActivateChild: [AdAuthGuardService],
            canActivate: [AuthGuard],
            data: { authPrefix: AuthResources.PUBLISH },
          },
          {
            path: 'cdm', // 运维中心cdm入口
            pathMatch: 'prefix',
            loadChildren: (): any =>
              import('../pages/devOps-center/cdm-wrap/cdm-wrap.module').then((m) => m.CdmWrapModule),
          },
          {
            path: 'prvm', // 运维中心prvm入口
            pathMatch: 'prefix',
            loadChildren: (): any =>
              import('../pages/devOps-center/cdm-wrap/cdm-wrap.module').then((m) => m.CdmWrapModule),
          },
        ],
      },
      // {
      //   path: 'application-management/config-area',
      //   pathMatch: 'prefix',
      //   component: DevOpsWrapComponent,
      // },
      // {
      //   path: 'app/application-management',
      //   component: DevOpsWrapComponent,
      // },
      // {
      //   path: 'app/application-management/deploy',
      //   pathMatch: 'prefix',
      //   component: ModuleWrapperComponent,
      // },
      // {
      //   path: 'app/application-management/module',
      //   pathMatch: 'prefix',
      //   component: DevOpsWrapComponent,
      // },
      // {
      //   path: 'app/application-management/iframe',
      //   pathMatch: 'prefix',
      //   component: DevOpsWrapComponent,
      // },
      // {
      //   path: 'deploy-authority',
      //   pathMatch: 'prefix',
      //   component: DevOpsWrapComponent,
      // },
      {
        path: 'micro-frontend',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../common/micro-frontend/micro-frontend.module').then((m) => m.MicroFrontendModule),
      },
      {
        path: 'help-center',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/help-center/help-center.module').then((m) => m.HelpCenterModule),
        canActivateChild: [AdAuthGuardService],
      },
      {
        path: 'manage-background',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../pages/manage-background/manage-background.module').then((m) => m.ManageBackgroundModule),
        canActivateChild: [AdAuthGuardService],
      },
      {
        path: 'test',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/test/test.module').then((m) => m.TestModule),
      },
      {
        path: 'ai-assistance',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../pages/ai/ai-assistance/ai-assistance.module').then((m) => m.AIAssinstanceModule),
        canActivateChild: [AdAuthGuardService],
      },
      {
        path: 'ai-three',
        pathMatch: 'prefix',
        loadChildren: (): any =>
          import('../pages/ai/three-difficulties/three-difficulties.module').then((m) => m.ThreeDifficultiesModule),
        canActivateChild: [AdAuthGuardService],
      },
      {
        path: 'asa-designer-web',
        pathMatch: 'prefix',
        loadChildren: (): any => import('../pages/asa-designer/asa-designer.module').then((m) => m.AsaDesignerModule),
        canLoad: [AdAuthGuardService, AppInfoGuard],
        canActivateChild: [AdAuthGuardService],
        canActivate: [AuthGuard],
        data: { authPrefix: AuthResources.APPLICATION },
        resolve: {
          selectedApp: SelectedAppResolver,
        },
      },
    ],
  },
  {
    path: 'api-query-view', // 首页无登录进入api设计
    pathMatch: 'full',
    component: ApiInquireComponent,
    canActivate: [NoticeGuard],
  },
  { path: 'dtd-designer', redirectTo: 'tools/dtd-designer', pathMatch: 'prefix' },
  {
    path: 'tools',
    pathMatch: 'prefix',
    loadChildren: (): any => import('../pages/tools/tools.module').then((m) => m.ToolsModule),
    canActivate: [NoticeGuard],
  },
  {
    path: 'sso-login',
    pathMatch: 'full',
    loadChildren: (): any => import('../pages/login/sso/sso-login.module').then((m) => m.SsoLoginModule),
    canActivate: [NoticeGuard],
  },
  {
    path: 'individual/sso-login',
    pathMatch: 'full',
    loadChildren: (): any => import('../pages/login/sso/sso-login.module').then((m) => m.SsoLoginModule),
    canActivate: [NoticeGuard, IndividualLoginGuard],
  },
  {
    path: 'login',
    pathMatch: 'full',
    loadChildren: () => import('../pages/login/login.module').then((m) => m.LoginModule),
    canActivate: [NoticeGuard],
  },
  {
    path: 'individual/login',
    pathMatch: 'full',
    loadChildren: () => import('../pages/login/login.module').then((m) => m.LoginModule),
    canActivate: [NoticeGuard, IndividualLoginGuard],
  },
  {
    path: 'errors',
    pathMatch: 'prefix',
    loadChildren: () => import('../pages/errors/errors.module').then((m) => m.ErrorsModule),
    canActivate: [NoticeGuard],
  },
  {
    // 未授权报错界面
    path: 'unauthorised',
    redirectTo: 'errors/unauthorised',
  },
  {
    path: 'open-vscode',
    pathMatch: 'prefix',
    loadChildren: () => import('../pages/open-vscode/open-vscode.module').then((m) => m.OpenVscodeModule),
  },
  {
    path: 'notice',
    pathMatch: 'prefix',
    loadChildren: () => import('../pages/notice/notice.module').then((m) => m.NoticeModule),
  },
  // {
  //   path: '**',
  //   redirectTo: '/404',
  // },
  // {
  //   path: '404',
  //   redirectTo: 'errors/404',
  // },
];

@NgModule({
  imports: [RouterModule.forRoot(routes, { relativeLinkResolution: 'legacy' })],
  exports: [RouterModule],
})
export class AppRoutingModule {}
