<p>pure-designer works!1</p>

<div class="app-pure-designer">
  <ul nz-menu nzMode="inline" *ngIf="menuList?.length > 0" [nzSelectable]="false">
    <!-- 渲染menus -->
    <ng-container *ngFor="let menu of menuList">
      <ng-container>
        <!-- 有children则渲染一二级菜单,否则渲染一级link-->
        <ng-container *ngIf="menu.children.length > 0">
          <ng-container *ngTemplateOutlet="secondaryMenuTpl; context: { menu, level: 1 }"></ng-container>
        </ng-container>

        <!-- 如果没有children则渲染一级link -->
        <ng-container *ngIf="menu.children.length === 0">
          <ng-container *ngTemplateOutlet="menuTpl; context: { menu, level: 1 }"></ng-container>
        </ng-container>
      </ng-container>
    </ng-container>

    <!-- Link菜单模板 -->
    <ng-template #menuTpl let-menu="menu" let-level="level">
      <ng-container>
        <li nz-menu-item class="submenu-list" [nzDisabled]="menu?.disabled" [nzPaddingLeft]="getOffsetLeft(level)">
          <a (click)="routerLinkTo(menu.path)" class="submenu-li">
            <!-- 默认视图 icon 图标 -->
            <span class="program-title">
              <i
                adIcon
                aria-hidden="true"
                *ngIf="!!getMenuIcon(menu)"
                [iconfont]="getMenuIcon(menu)"
                class="icon-menu-style"
              ></i>
              <span class="icon-menu-name"> 2{{ menu.name }} </span>
            </span>
          </a>
        </li>
      </ng-container>
    </ng-template>

    <!-- 一二级菜单模板  -->
    <ng-template #secondaryMenuTpl let-menu="menu" let-level="level">
      <li
        nz-submenu
        [nzTitle]="titleTmpl"
        [class]="{ 'submenu-title': true }"
        [nzDisabled]="menu?.disabled"
        [nzOpen]="menu.open"
        routerLinkActive="parent-selected"
        [routerLinkActiveOptions]="{ exact: false }"
        [nzPaddingLeft]="level === 3 ? 44 : level === 1 ? 18 : 32"
        (nzOpenChange)="this.changeSubMenuOpen($event, menu)"
        [routerLink]="menu.path"
      >
        <ng-template #titleTmpl>
          0
          <div
            [class]="{
              'title-menu-item-level1': level === 1,
              'title-menu-item-level2': level === 2,
              'title-menu-item-level3': level === 3
            }"
          >
            <i
              adIcon
              [class]="{
                openIcon: true,
                iconfont: true,
                'title-menu-icon-level1': level === 1,
                'title-menu-icon-level2': level === 2,
                'title-menu-icon-level3': level === 3
              }"
              [type]="menu.open ? 'caret-down' : 'caret-right'"
              theme="outline"
            ></i>

            <span class="program-title">
              <i
                adIcon
                aria-hidden="true"
                *ngIf="!!getMenuIcon(menu)"
                [iconfont]="getMenuIcon(menu)"
                class="icon-menu-style"
              ></i>
              <span
                nz-tooltip
                [nzTooltipTitle]="menu.lang?.name?.[('dj-LANG' | translate)] ?? menu.name "
                class="icon-menu-name"
                [class]="{
                  'icon-menu-name-businessConstructor': menu.type === 'businessConstructor'
                }"
              >
                1{{ menu.name }}
              </span>
            </span>
          </div>
        </ng-template>
        <ul *ngIf="menu.open">
          <!-- 渲染Link菜单 -->
          <ng-container *ngFor="let item of menu.children">
            <!-- 有children则渲染一二级菜单,否则渲染一级link-->
            <ng-container *ngIf="item.children.length > 0">
              <ng-container
                *ngTemplateOutlet="secondaryMenuTpl; context: { menu: item, level: level + 1 }"
              ></ng-container>
            </ng-container>

            <!-- 如果没有children则渲染一级link -->
            <ng-container *ngIf="item.children.length === 0">
              <ng-container *ngTemplateOutlet="menuTpl; context: { menu: item, level: level + 1 }"></ng-container>
            </ng-container>
          </ng-container>
        </ul>
      </li>
    </ng-template>
  </ul>
  <router-outlet></router-outlet>
</div>
