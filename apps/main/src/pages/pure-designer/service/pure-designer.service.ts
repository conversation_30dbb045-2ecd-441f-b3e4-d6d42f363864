import { Injectable } from '@angular/core';
import { TranslateService } from '@ngx-translate/core';
import { Observable, Subject } from 'rxjs';

@Injectable()
export class PureDesignerService {
  // ========================= 模块级属性 =========================
  private _test: boolean = true;
  get test(): boolean {
    return this.test;
  }

  // ========================= 模块级属性的操作 =========================
  setTest(isShow: boolean): void {
    this._test = isShow;
  }

  // 重置所有数据
  resetAll(): void {
    this._test = true;
  }
}
