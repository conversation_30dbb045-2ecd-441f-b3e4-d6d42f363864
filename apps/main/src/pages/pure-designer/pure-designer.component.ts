import { Component, OnInit } from '@angular/core';
import { PureDesignerRequestService } from './service/pure-designer-request.service';

@Component({
  selector: 'app-pure-designer',
  templateUrl: './pure-designer.component.html',
  styleUrls: ['./pure-designer.component.less'],
  providers: [PureDesignerRequestService],
})
export class PureDesignerComponent implements OnInit {
  menuList = [
    {
      name: '作业',
      path: '',
      open: true,
      children: [
        {
          name: '作业1',
          path: '',
          open: true,
          children: [
            {
              name: '页面-1',
              path: 'page-designer',
              open: true,
              children: [],
            },
            {
              name: '页面-2',
              path: 'page-designer',
              open: true,
              children: [],
            },
          ],
        },
      ],
    },
  ];

  constructor() {}

  ngOnInit(): void {}
}
