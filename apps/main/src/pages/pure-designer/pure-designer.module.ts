import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { PureDesignerComponent } from './pure-designer.component';
import { PureDesignerRoutingModule } from './pure-designer-routing.module';
import { PureDesignerService } from './service/pure-designer.service';
import { NzMenuModule } from 'ng-zorro-antd/menu';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';

@NgModule({
  declarations: [PureDesignerComponent],
  imports: [CommonModule, PureDesignerRoutingModule, NzMenuModule, AdIconModule, NzToolTipModule],
  providers: [PureDesignerService],
})
export class PureDesignerModule {}
