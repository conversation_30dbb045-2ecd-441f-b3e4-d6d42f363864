import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { PureDesignerComponent } from './pure-designer.component';

const routes: Routes = [
  {
    path: '',
    pathMatch: 'prefix',
    component: PureDesignerComponent,
    children: [
      {
        path: 'page-designer',
        pathMatch: 'prefix',
        loadChildren: () => import('./pages/page-designer/page-designer.module').then((m) => m.PageDesignerModule),
      },
    ],
  },
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule],
})
export class PureDesignerRoutingModule {}
