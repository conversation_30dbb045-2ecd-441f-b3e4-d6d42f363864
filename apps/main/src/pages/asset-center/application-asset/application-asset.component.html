<div class="application-asset">
  <!-- 暂时隐藏tabs -->
  <!-- <ad-tabs
    class="application-asset-tabs"
    nzSize="small"
    nzType="card"
    nzSelectedIndexChange="handleSelectedIndexChange"
  >
    <ng-container *ngFor="let tab of tabList">
      <ad-tab class="tab-container" [nzClosable]="false" [nzTitle]="tab.title | translate"></ad-tab>
    </ng-container>
  </ad-tabs> -->

  <div class="application-asset-content">
    <div class="search-input">
      <nz-input-group [nzSuffix]="suffixIconSearch" class="search-input-group">
        <input
          type="text"
          nz-input
          [placeholder]="'dj-请输入关键字' | translate"
          [(ngModel)]="queryValue"
          (ngModelChange)="handleSearch($event)"
        />
      </nz-input-group>
      <ng-template #suffixIconSearch>
        <span class="input-clear" *ngIf="!!queryValue">
          <i adIcon type="close-circle" theme="fill" (click)="handleClearSearch()"></i>
        </span>
        <span adIcon nzDot type="search" [style]="{ color: 'rgba(0, 0, 0, 0.4)' }"></span>
      </ng-template>
      <nz-badge [nzShowZero]="false" [nzCount]="advancedQueryValue?.length ?? 0" [nzOffset]="[4, 6]">
        <span class="advanced-query-btn" (click)="handleOpenAdvancedQueryModal()">
          <i adIcon [iconfont]="'iconinputserach'"></i>
          {{ 'dj-高级搜索' | translate }}
        </span>
      </nz-badge>
    </div>

    <div class="table-container">
      <!-- [nzScroll]="{ y: tableHeight }" -->
      <nz-table
        *ngIf="assetTableData.length || assetTableLoading"
        #dtdAssetTable
        [nzScroll]="{ x: '1650px' }"
        nzTableLayout="fixed"
        [nzData]="assetTableData"
        [nzBordered]="false"
        nzShowPagination
        [nzLoading]="assetTableLoading"
        [nzTotal]="assetTablePagination.total"
        [nzShowSizeChanger]="true"
        [nzShowQuickJumper]="true"
        [nzPageSize]="assetTablePagination.pageSize"
        [nzPageIndex]="assetTablePagination.pageNum"
        [nzShowTotal]="totalTemplate"
        [nzFrontPagination]="false"
        (nzPageIndexChange)="handleChangePageIndex($event)"
        (nzPageSizeChange)="handleChangePageSize($event)"
      >
        <thead>
          <tr>
            <th
              *ngFor="let column of assetTableColumn"
              [nzEllipsis]="column.ellipsis"
              [nzWidth]="column.width"
              [nzRight]="column.right"
            >
              {{ column.title | translate }}
            </th>
          </tr>
        </thead>
        <tbody>
          <ng-template ngFor let-data [ngForOf]="dtdAssetTable.data" let-i="index">
            <tr>
              <td
                nzEllipsis
                nz-tooltip
                [nzTooltipTitle]="tooltipTemplate"
                [nzTooltipTitleContext]="{ $implicit:  (data.hasResult ? '' : '(' + ('dj-成果已移除' | translate) + ')') + (data?.lang?.assetName?.[currentLang] ?? data.assetName  ?? '--')}"
              >
                <span *ngIf="!data.hasResult" class="application-asset-table-tag error">{{
                  'dj-成果已移除' | translate
                }}</span>
                <span>{{data?.lang?.assetName?.[currentLang] ?? data.assetName  ?? '--' }}</span>
              </td>
              <td nzEllipsis nz-tooltip>{{ (data.source === 'CB' ? 'dj-组合资产' : 'dj-独立资产') | translate }}</td>
              <td nzEllipsis nz-tooltip>{{ data.assetId }}</td>
              <td [nzAlign]="'center'" nzEllipsis nz-tooltip>
                <span class="content-use-method">
                  <i class="can-icon" adIcon [iconfont]="'icongou-xian'" *ngIf="!!data?.canReference"></i>
                  <i class="can-not-icon" adIcon [iconfont]="'iconguanbi-xian'" *ngIf="!data?.canReference"></i>
                </span>
              </td>
              <td [nzAlign]="'center'" nzEllipsis nz-tooltip>
                <span class="content-use-method">
                  <i class="can-icon" adIcon [iconfont]="'icongou-xian'" *ngIf="!!data?.canCopy"></i>
                  <i class="can-not-icon" adIcon [iconfont]="'iconguanbi-xian'" *ngIf="!data?.canCopy"></i>
                </span>
              </td>
              <td
                nzEllipsis
                nz-tooltip
                [nzTooltipTitle]="data?.tags?.length > 0 && tooltipTemplate"
                [nzTooltipTitleContext]="{ $implicit: data?.tags?.join(',') }"
              >
                <span class="application-asset-table-tag" *ngFor="let tag of data?.tags ?? []">{{ tag }}</span>
              </td>
              <td
                nzEllipsis
                nz-tooltip
                [nzTooltipTitle]="tooltipTemplate"
                [nzTooltipTitleContext]="{ $implicit: data?.lang?.assetDesc?.[currentLang] ?? data.assetDesc  ?? '--'}"
              >
                {{ data?.lang?.assetDesc?.[currentLang] ?? data.assetDesc  ?? '--' }}
              </td>
              <td nzEllipsis nz-tooltip>{{ data.publisherName }}</td>
              <td nzEllipsis nz-tooltip>{{ data.copiedCount + data.referencedCount }}</td>
              <td nzEllipsis nz-tooltip>
                <span class="table-operation" (click)="handleOpenReference(data)">{{ data.curReferenceCount }}</span>
              </td>
              <td nzRight>
                <span class="table-operation" (click)="handleOpenBaseInfo(data)">{{ 'dj-详情' | translate }}</span>
                <span
                  *ngIf="currentTenantId === data.publisher"
                  class="table-operation"
                  (click)="handleEnterApp(data)"
                  >{{ 'dj-进入应用' | translate }}</span
                >
                <ng-container *operateAuth="{ prefix: 'update' }">
                  <span *ngIf="data.openRange !== 'all'" class="table-operation" (click)="handleRemove(data)">{{
                    'dj-下架' | translate
                  }}</span>
                </ng-container>
              </td>
            </tr>
          </ng-template>
        </tbody>
      </nz-table>
      <ng-template #totalTemplate let-total>{{
        'dj-共n项1' | translate: { total: assetTablePagination.total }
      }}</ng-template>
      <div *ngIf="!assetTableData.length && !assetTableLoading" class="no-data">
        <img src="/assets/img/designer/no_data.png" alt="" />
        <div class="text">{{ 'dj-暂无数据' | translate }}</div>
      </div>
    </div>
  </div>

  <ng-template #tooltipTemplate let-data>
    <div [ngStyle]="{ maxHeight: '500px', overflowY: 'auto' }">{{ data }}</div>
  </ng-template>

  <app-asset-info-drawer
    [visible]="isAssetInfoDrawerlVisible"
    [dataInfo]="assetInfoDrawerlInfo"
    (emitClose)="handleCloseBaseInfo()"
  ></app-asset-info-drawer>

  <app-asset-reference-drawer
    [visible]="isAssetReferenceDrawerlVisible"
    [isAssetCenter]="true"
    [dataInfo]="assetReferenceDrawerlInfo"
    (emitClose)="handleCloseReference()"
  ></app-asset-reference-drawer>

  <app-advanced-query-modal
    [visible]="isAdvancedQueryModalVisible"
    [initialValue]="advancedQueryValue"
    (onSubmit)="handleSubmitAdvancedQueryModal($event)"
    (onClose)="handleCloseAdvancedQueryModal()"
  ></app-advanced-query-modal>
</div>
