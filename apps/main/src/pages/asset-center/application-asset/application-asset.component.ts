import { Component, OnInit, On<PERSON>hanges, OnDestroy, SimpleChanges, AfterViewInit } from '@angular/core';
import { ApplicationAssetService } from './service/application-asset.service';
import { ApplicationAssetRequestService } from './service/application-asset-request.service';
import { Tab, TabType, AssetInfo, PaginationObject, AnyFunction } from './config/application-asset.type';
import { tabList as tabListConfig, assetTableColumns } from './config/application-asset.config';
import { TranslateService } from '@ngx-translate/core';
import { QueryGroup } from './components/advanced-query-modal/advanced-query-modal.type';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { Router } from '@angular/router';
import { IndividualService } from 'pages/individual/individual.service';
import { AppService } from 'pages/apps/app.service';
import { debounce, DebouncedFunc, trim } from 'lodash';
import { NzMessageService } from 'ng-zorro-antd/message';
import { AdUserService } from 'pages/login/service/user.service';

@Component({
  selector: 'app-dtd-asset',
  templateUrl: './application-asset.component.html',
  styleUrls: ['./application-asset.component.less'],
  providers: [ApplicationAssetService, ApplicationAssetRequestService],
})
export class ApplicationAssetComponent implements OnInit, AfterViewInit, OnChanges, OnDestroy {
  tabList: Tab[] = tabListConfig; // tab列表
  activeTab: Tab = this.tabList[0]; // 当前激活的tab

  queryValue: string = ''; // 查询条件
  advancedQueryValue: QueryGroup[] = null; // 高级查询条件

  // 表格相关分页对象
  assetTablePagination: PaginationObject = {
    pageSize: 10,
    pageNum: 1,
    total: 100,
  };
  assetTableLoading: boolean = false; // 资产表格数据加载
  assetTableColumn = assetTableColumns; // 资产表格列定义
  assetTableData: AssetInfo[] = []; // 资产表格数据

  isAssetInfoDrawerlVisible: boolean = false; // 资产信息开窗是否显示
  assetInfoDrawerlInfo: AssetInfo = null; // 资产信息

  isAdvancedQueryModalVisible: boolean = false; // 高级查询弹窗是否显示

  isAssetReferenceDrawerlVisible: boolean = false; // 资产信息开窗是否显示
  assetReferenceDrawerlInfo: AssetInfo = null; // 资产信息

  updateAssetTableDebounce: DebouncedFunc<AnyFunction> = null; // 更新表格数据的防抖函数

  currentTenantId: string = null; // 当前租户id

  get currentLang(): string {
    return this.translateService?.currentLang || 'zh_CN';
  } // 当前语言

  constructor(
    public applicationAssetService: ApplicationAssetService,
    private ApplicationAssetRequestService: ApplicationAssetRequestService,
    private translateService: TranslateService,
    private modal: AdModalService,
    private router: Router,
    private individualService: IndividualService,
    private app: AppService,
    private athMessageService: NzMessageService,
    private userService: AdUserService,
  ) {
    this.updateAssetTableDebounce = debounce(this.updateAssetTable, 200);
    this.currentTenantId = this.userService.getUser('tenantId');
  }

  ngOnInit(): void {}

  ngAfterViewInit(): void {
    this.updateAssetTableDebounce();
  }

  ngOnChanges(changes: SimpleChanges): void {}

  ngOnDestroy(): void {}

  // ======= 接口请求 =======
  // 请求资产列表
  assetCenterListRequest(params: unknown, type: TabType): Promise<unknown> {
    return this.ApplicationAssetRequestService.assetCenterList(params, type).toPromise();
  }

  // 资产下架（资产中心）
  removedAssetOpenTenantRequest(params: unknown): Promise<unknown> {
    return this.ApplicationAssetRequestService.removedAssetOpenTenant(params).toPromise();
  }

  // ======= 接口处理 =======
  // 更新资产表格
  async updateAssetTable(): Promise<void> {
    const params = {
      pageSize: this.assetTablePagination.pageSize,
      pageNum: this.assetTablePagination.pageNum,
      condition: trim(this.queryValue),
      advancedQueryParams: {
        queryGroups: this.advancedQueryValue ?? [],
      },
    };

    try {
      this.assetTableLoading = true;
      const assetCenterListRes = await this.assetCenterListRequest(params, this.activeTab.type);
      const { curPageNum, limit, total, data: assetList } = (assetCenterListRes as { data: any }).data;
      this.assetTablePagination = {
        pageSize: limit,
        pageNum: curPageNum,
        total: total,
      };
      this.assetTableData = assetList;
    } catch (error) {
      console.log('handleConfirm error:', error);
    } finally {
      this.assetTableLoading = false;
    }
  }

  // 下架资产
  async handleRemovedAssetOpenTenant(assetId: string): Promise<void> {
    const params = {
      assetId,
    };
    try {
      this.assetTableLoading = true;
      await this.removedAssetOpenTenantRequest(params);
      this.athMessageService.success(this.translateService.instant('dj-操作成功'));
      this.updateAssetTable();
    } catch (error) {
      console.log('handleConfirm error:', error);
    } finally {
      this.assetTableLoading = false;
    }
  }

  // ======= 其他业务逻辑 =======
  // 切换tab页
  handleSelectedIndexChange(index: number): void {
    this.activeTab = this.tabList[index];
    this.assetTablePagination.pageNum = 1;
    this.updateAssetTableDebounce();
  }

  // 搜索
  handleSearch(data): void {
    this.queryValue = data;
    this.assetTablePagination.pageNum = 1;
    this.updateAssetTableDebounce();
  }

  // 清空搜索
  handleClearSearch(): void {
    this.queryValue = '';
    this.handleSearch('');
  }

  // 切页
  handleChangePageIndex(index: number): void {
    this.assetTablePagination.pageNum = index;
    this.updateAssetTableDebounce();
  }

  // 改变每页条数
  handleChangePageSize(size: number): void {
    this.assetTablePagination.pageSize = size;
    this.updateAssetTableDebounce();
  }

  // 打开高级查询
  handleOpenAdvancedQueryModal(): void {
    this.isAdvancedQueryModalVisible = true;
  }

  // 关闭高级查询
  handleCloseAdvancedQueryModal(): void {
    this.isAdvancedQueryModalVisible = false;
  }

  // 确认高级查询
  handleSubmitAdvancedQueryModal(data: QueryGroup[]): void {
    this.advancedQueryValue = data;
    this.assetTablePagination.pageNum = 1;
    this.updateAssetTableDebounce();
    this.handleCloseAdvancedQueryModal();
  }

  // 打开基础信息抽屉
  handleOpenBaseInfo(data: AssetInfo): void {
    console.log('handleOpenBaseInfo');
    this.assetInfoDrawerlInfo = data;
    this.isAssetInfoDrawerlVisible = true;
  }

  // 关闭基础信息抽屉
  handleCloseBaseInfo(): void {
    this.isAssetInfoDrawerlVisible = false;
  }

  // 打开引用抽屉
  handleOpenReference(data: AssetInfo): void {
    console.log('handleOpenReference');
    this.assetReferenceDrawerlInfo = data;
    this.isAssetReferenceDrawerlVisible = true;
  }

  // 关闭引用抽屉
  handleCloseReference(): void {
    console.log('handleCloseReference');
    this.isAssetReferenceDrawerlVisible = false;
  }

  // 进入应用
  handleEnterApp(data: AssetInfo): void {
    console.log('handleEnterApp');
    const url = this.router.serializeUrl(
      this.router.createUrlTree(
        this.individualService.individualCase
          ? ['individual', 'app', 'application-overview', 'asset-openness']
          : ['app', 'application-overview', 'asset-openness'],
        {
          queryParams: { appCode: data.application },
        },
      ),
    );
    window.open(url, '_blank');
  }

  // 下架
  handleRemove(data: AssetInfo): void {
    this.modal.confirm({
      nzTitle: this.translateService.instant('dj-下架该资产'),
      nzContent: this.translateService.instant(
        'dj-资产下架后，资产中心不可见该资产，仅应用内可见，对已经将资产拷贝至应用内，和创建资产引用关联的应用不会产生影响',
      ),
      nzOkText: this.translateService.instant('dj-确定'),
      nzOnOk: () => {
        this.handleRemovedAssetOpenTenant(data.assetId);
      },
      nzCancelText: this.translateService.instant('dj-取消'),
      nzOnCancel: () => {},
    });
  }
}
