<nz-drawer
  nzWrapClassName="asset-reference-drawer"
  [nzTitle]="'dj-资产当前引用' | translate"
  [nzContent]="modalContent"
  [nzFooter]="null"
  [nzWidth]="'480px'"
  [(nzVisible)]="visible"
  [nzMaskClosable]="false"
  [nzClosable]="true"
  (nzOnClose)="handleCancel()"
>
  <ng-template #modalContent>
    <div class="asset-reference-drawer-content">
      <div class="header-wrapper">
        <nz-input-group [nzSuffix]="suffixIconSearch" class="search-input">
          <input
            type="text"
            nz-input
            [placeholder]="'dj-请输入关键字' | translate"
            [(ngModel)]="queryValue"
            (ngModelChange)="handleSearch($event)"
          />
        </nz-input-group>
        <ng-template #suffixIconSearch>
          <span adIcon nzDot type="search" [style]="{ color: 'rgba(0, 0, 0, 0.4)' }"></span>
        </ng-template>
      </div>

      <div class="table-container">
        <nz-table
          *ngIf="tableData?.length || tableLoading"
          #table
          nzTableLayout="fixed"
          [nzShowPagination]="false"
          [nzData]="tableData"
          [nzBordered]="false"
          [nzLoading]="tableLoading"
          [nzScroll]="{ y: tableHeight }"
        >
          <thead>
            <tr>
              <th *ngFor="let column of tableColumn" [nzEllipsis]="column.ellipsis" [nzWidth]="column.width">
                {{ column.title | translate }}
              </th>
            </tr>
          </thead>
          <tbody>
            <ng-template ngFor let-data [ngForOf]="table.data" let-i="index">
              <tr>
                <td nzEllipsis nz-tooltip>
                  {{ i + 1 }}
                </td>
                <td nzEllipsis nz-tooltip>
                  {{data?.lang?.name?.[currentLang] ?? data.name  ?? '--' }}
                </td>
                <td nzEllipsis nz-tooltip>
                  {{ data?.tenantId ?? '--' }}
                </td>
              </tr>
            </ng-template>
          </tbody>
        </nz-table>
        <ng-template #totalTemplate let-total>{{
          'dj-共n项1' | translate: { total: tablePagination.total }
        }}</ng-template>
        <div *ngIf="!tableData?.length && !tableLoading" class="no-data">
          <img src="/assets/img/designer/no_data.png" alt="" />
          <div class="text">{{ 'dj-暂无数据' | translate }}</div>
        </div>
      </div>
    </div>
  </ng-template>
</nz-drawer>
