<!-- [nzFooter]="footerTpl" -->
<nz-drawer
  nzWrapClassName="asset-info-drawer"
  [nzTitle]="'dj-资产详情' | translate"
  [nzContent]="modalContent"
  [nzWidth]="'480px'"
  [(nzVisible)]="visible"
  [nzMaskClosable]="false"
  [nzClosable]="true"
  (nzOnClose)="handleCancel()"
>
  <ng-template #modalContent>
    <nz-spin [nzSpinning]="isLoading">
      <div class="asset-info-drawer-content">
        <div class="content-row">
          <div class="content-row-title">{{ 'dj-资产名称' | translate }}:</div>
          <div class="content-row-content">
            {{assetDetail?.lang?.assetName?.[currentLang] ?? assetDetail?.assetName  ?? '--' }}
            <span class="content-row-tag" *ngFor="let tag of assetDetail?.tags ?? []">{{ tag }}</span>
          </div>
        </div>
        <div class="content-row">
          <div class="content-row-title">{{ 'dj-代号' | translate }}:</div>
          <div class="content-row-content">{{ assetDetail?.assetId }}</div>
        </div>
        <div class="content-row">
          <div class="content-row-title">{{ 'dj-类型' | translate }}:</div>
          <div class="content-row-content">
            {{ (assetDetail?.source === 'CB' ? 'dj-组合资产' : 'dj-独立资产') | translate }}
          </div>
        </div>
        <div class="content-row">
          <div class="content-row-title">{{ 'dj-描述' | translate }}:</div>
          <div class="content-row-content">
            {{ assetDetail?.lang?.assetDesc?.[currentLang] ?? assetDetail?.assetDesc  ?? '--' }}
          </div>
        </div>
        <div class="content-row">
          <div class="content-row-title">{{ 'dj-发布方' | translate }}:</div>
          <div class="content-row-content">{{ assetDetail?.publisherName ?? '--' }}</div>
        </div>
        <div class="content-row">
          <div class="content-row-title">{{ 'dj-复用方式' | translate }}:</div>
          <div class="content-row-content">
            <span class="content-use-method">
              <i class="can-icon" adIcon [iconfont]="'icongou-xian'" *ngIf="!!assetDetail?.canReference"></i>
              <i class="can-not-icon" adIcon [iconfont]="'iconguanbi-xian'" *ngIf="!assetDetail?.canReference"></i>
              <span>{{ (!!assetDetail?.canReference ? 'dj-可引用' : 'dj-不可引用') | translate }}</span>
              <i
                adIcon
                nz-tooltip
                [nzTooltipTitle]="'dj-可创建对该资产的引用关联，依赖该资产，后续资产跟随发布方自动版更' | translate"
                class="help-icon"
                [iconfont]="'iconqipaotishi-wenhao'"
              ></i>
            </span>
            <span class="content-use-method">
              <i class="can-icon" adIcon [iconfont]="'icongou-xian'" *ngIf="!!assetDetail?.canCopy"></i>
              <i class="can-not-icon" adIcon [iconfont]="'iconguanbi-xian'" *ngIf="!assetDetail?.canCopy"></i>
              <span>{{ (!!assetDetail?.canCopy ? 'dj-可复制' : 'dj-不可复制') | translate }}</span>
            </span>
          </div>
        </div>
        <div class="content-row" *ngIf="!!assetDetail?.canCopy">
          <div class="content-row-title">{{ 'dj-可复制版本' | translate }}:</div>
          <div class="content-row-content">
            <span class="content-row-use-tag" *ngFor="let copyableItem of assetDetail?.copyableItems ?? []">
              {{ branchLangKeyObject[copyableItem.sourceBranch] | translate }}V{{ copyableItem.adpRemarkNumber }}
            </span>
          </div>
        </div>
      </div>
    </nz-spin>
  </ng-template>
  <!-- <ng-template #footerTpl>
    <div class="asset-info-drawer-footer">
      <ng-container *operateAuth="{ prefix: 'update' }">
        <button ad-button adType="primary" (click)="handleView()">
          {{ 'dj-查看资产成果' | translate }}
        </button>
      </ng-container>
    </div>
  </ng-template> -->
</nz-drawer>
