import { Component, EventEmitter, Input, OnDestroy, OnInit, Output, SimpleChanges, OnChanges } from '@angular/core';
import { AssetInfoDrawerRequestService } from './asset-info-drawer-request.service';
import { TranslateService } from '@ngx-translate/core';
import { AssetInfo } from './asset-info-drawer.type';
import { ActivatedRoute, Router } from '@angular/router';
@Component({
  selector: 'app-asset-info-drawer',
  templateUrl: './asset-info-drawer.component.html',
  styleUrls: ['./asset-info-drawer.component.less'],
  providers: [AssetInfoDrawerRequestService],
})
export class AssetInfoDrawerComponent implements OnInit, OnChanges, OnDestroy {
  @Input() visible: boolean = false;
  @Input() dataInfo: AssetInfo;
  @Output() emitClose: EventEmitter<any> = new EventEmitter();
  isLoading: boolean = false;
  assetDetail: AssetInfo;
  get currentLang(): string {
    return this.translate?.currentLang || 'zh_CN';
  } // 当前语言

  branchLangKeyObject = {
    ['develop']: 'dj-开发分支',
    ['test']: 'dj-测试分支',
    ['master']: 'dj-正式分支',
    ['bak']: 'dj-备份分支',
  };

  constructor(
    public service: AssetInfoDrawerRequestService,
    private translate: TranslateService,
    private router: Router,
  ) {}

  ngOnInit(): void {}

  ngOnChanges(changes: SimpleChanges): void {
    if (Object.keys(changes).includes('visible') && changes.visible.currentValue) {
      this.handleInit();
    }
  }

  // ======= 接口请求 =======
  // 获取资产详情
  assetDetailRequest(params: unknown): Promise<unknown> {
    return this.service.assetDetail(params).toPromise();
  }

  // 资产版本列表
  assetVersionListRequest(params: unknown): Promise<unknown> {
    return this.service.assetVersionList(params).toPromise();
  }

  // ======= 接口处理 =======
  // 更新资产详情
  async updateAssetDetail(): Promise<any> {
    const params = {
      assetId: this.dataInfo?.assetId,
    };

    try {
      this.isLoading = true;
      const assetDetailRes = await this.assetDetailRequest(params);
      const { data: assetDetail } = assetDetailRes as { data: any };
      return assetDetail;
    } catch (error) {
      console.log('handleConfirm error:', error);
    } finally {
      this.isLoading = false;
    }
  }

  // 更新资产版本列表
  async updateAssetVersionList(assetId: string): Promise<any> {
    const params = {
      assetId,
    };

    try {
      this.isLoading = true;
      const assetVersionListRes = await this.assetVersionListRequest(params);
      const { data: assetVersionList } = assetVersionListRes as { data: any };
      return assetVersionList;
    } catch (error) {
      console.log('handleConfirm error:', error);
      return null;
    } finally {
      this.isLoading = false;
    }
  }

  // ======= 其他业务逻辑 =======
  async handleInit(): Promise<void> {
    const assetDetail = await this.updateAssetDetail();
    const assetVersionList = await this.updateAssetVersionList(assetDetail.assetId);

    this.assetDetail = {
      ...assetDetail,
      copyableItems: assetVersionList.filter((assetVersion) =>
        assetDetail.copyableItems.includes(assetVersion.objectId),
      ),
    };
  }

  // handleView(): void {
  //   console.log('handleView');
  //   const url = `/asset-center/dtd-designer-view`;
  //   const queryParams = {
  //     appCode: 'M7f60-33e7',
  //     projectCategory: 'single',
  //     projectCode: 'PM_6c31f302100025eb',
  //   };

  //   this.router.navigate([url], {
  //     queryParams,
  //   });
  // }

  handleCancel(): void {
    this.emitClose.emit();
  }

  ngOnDestroy(): void {}
}
