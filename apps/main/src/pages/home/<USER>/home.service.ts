import { HttpClient } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { Observable, Subject } from 'rxjs';
import { SystemConfigService } from 'common/service/system-config.service';

@Injectable()
export class HomeService {
  adesignerUrl: string;
  iamUrl: string;
  publishUrl: string;
  appRefresh$: Subject<any> = new Subject();

  constructor(protected configService: SystemConfigService, protected http: HttpClient) {
    this.configService.getConfig().subscribe((config) => {
      this.adesignerUrl = config.adesignerUrl;
      this.iamUrl = config.iamUrl;
      this.publishUrl = config.publishUrl;
    });
  }

  getExampleApp(type?: number): Observable<any> {
    return this.http.get(
      `${this.adesignerUrl}/athena-designer/application/getExampleApp`,
      type === undefined
        ? undefined
        : {
            params: { appType: type },
          },
    );
  }

  queryTenantPipeline(): Observable<any> {
    return this.http.get(`${this.publishUrl}/athenadeployer/releasePipeline/queryTenantPipeline4IndexPage`, {});
  }

  installExampleApp(params): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/application/installExampleApp`, params);
  }

  appInitData(params): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/appInitData`, params);
  }

  /** 获取发布的进度 */
  getProcessByUuid(uuid): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/appInitData/getProcessByUuid?uuid=${uuid}`);
  }

  /** 更新发布接口进度 */
  updateProcessByUuid(params): Observable<any> {
    return this.http.post(`${this.adesignerUrl}/athena-designer/appInitData/updateProcessByUuid`, params);
  }

  /**
   * 首页查看解决方案列表
   * @param params
   * @returns
   */
  queryHomeAppList(params): Observable<any> {
    const { currentKey, appType, condition } = params;
    let url = `${this.adesignerUrl}/athena-designer/application/queryRecentCreate?appType=${appType}&condition=${condition}`;
    if (currentKey === 'recentVisited') {
      url = `${this.adesignerUrl}/athena-designer/application/queryRecentVisit?appType=${appType}&condition=${condition}&num=20`;
    }
    return this.http.get(url);
  }

  /**
   * 查询解决方案的过期时间
   * @param codes
   * @returns
   */
  queryExperienceOverTime(codes: string[]): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/application/queryExperienceOverTime`;
    return this.http.post(url, codes);
  }

  /**
   * 获取可以访问哪些类型的解决方案
   * @returns
   */
  async queryIamPermission(param: any): Promise<number[]> {
    const url = `${this.iamUrl}/api/iam/v2/permission/user`;
    const result = (await this.http.post(url, param).toPromise()) as any;
    const map = new Map<string, number>([
      ['TYPE_DTD', 1],
      ['TYPE_TTT', 5],
      ['TYPE_FREECONTROL', 4],
      ['TYPE_AGILEDATA', 6],
      ['TYPE_NANA', 7],
    ]);
    const data = (result?.data || []).filter((e) => map.has(e.moduleId) && e.effect === 'allow');
    return data.map((item) => map.get(item.moduleId));
  }

  /**
   * 查询新手任务
   * @returns
   */
  async queryNewbieTask(): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/newbieTask/query?appType=${'TYPE_TTT'}`;
    return this.http.get(url).toPromise();
  }

  /**
   * 关闭新手任务
   * @returns
   */
  async closeNewbieTask(): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/newbieTask/close`;
    return this.http.post(url, { appType: 'TYPE_TTT' }).toPromise();
  }

  /**
   * 任务状态更新
   * @returns
   */
  async updateTaskStatus(): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/newbieTask/updateTaskStatus`;
    return this.http.post(url, { appType: 'TYPE_TTT' }).toPromise();
  }

  /**
   * 解决方案安装
   * @returns
   */
  async installApplication(params: any): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/newbieTask/installApplication`;
    return this.http.post(url, params).toPromise();
  }

  /**
   * 新的删除App
   * @param code
   */
  async deleteNewApp(code: string): Promise<any> {
    const url = `${this.adesignerUrl}/athena-designer/application/delete/v2/${code}`;
    return await this.http.get(url).toPromise();
  }

  // 删除App 废弃
  deleteApp(code: string): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/application/delete/${code}`;
    return this.http.get(url);
  }

  /**
   * 创建解决方案
   * @param params
   * @returns
   */
  createSolution(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/solution`;
    return this.http.post(url, params);
  }

  /**
   * 卡片授权查询
   * @param params {appType: number}
   * @returns
   */
  verifySolutionPermission(params): Observable<any> {
    const url = `${this.adesignerUrl}/athena-designer/application/verifySolutionPermission`;
    return this.http.get(url, { params });
  }

  /**
   * 获取所有通知
   * @returns
   */
  queryMessageNotice(): Observable<any> {
    return this.http.get(`${this.adesignerUrl}/athena-designer/messageNotification/queryAll`);
  }
}
