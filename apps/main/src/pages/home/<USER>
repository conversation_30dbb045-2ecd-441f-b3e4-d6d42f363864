import { CommonModule } from '@angular/common';
import { NgModule } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { CreateBlankComponent } from './create-blank/create-blank.component';
import { CreateTemplateComponent } from './create-template/create-template.component';
import { HomeRoutingModule } from './home-routing.module';
import { HomeComponent } from './home.component';
import { NzSpinModule } from 'ng-zorro-antd/spin';
import { CreateAppModule } from '../apps/create-app/create-app.module';
import { HomeService } from './service/home.service';
import { NzCarouselModule } from 'ng-zorro-antd/carousel';
import { BeginnerTutorialComponent } from './beginner-tutorial/beginner-tutorial.component';
import { CreateAIComponent } from './create-ai/create-ai.component';
import { ProductBlockComponent } from './product-block/product-block.component';
import { HomeAiCreateModalComponent } from '../ai/ai-assistance/components/home-create-modal/home-create-modal.component';
import { NzToolTipModule } from 'ng-zorro-antd/tooltip';
import { TranslateModule } from '@ngx-translate/core';
import { DirectiveModule } from '../../common/directive/directive.module';
import { AdButtonModule } from 'components/ad-ui-components/ad-button/ad-button.module';
import { AdModalModule } from 'components/ad-ui-components/ad-modal/ad-modal.module';
import { AdEmptyModule } from 'components/ad-ui-components/ad-empty/ad-empty.module';
import { AISelectAppComponent } from '../ai/ai-assistance/components/select-app/select-app.component';
import { NzInputModule } from 'ng-zorro-antd/input';
import { NzTableModule } from 'ng-zorro-antd/table';
import { AdIconModule } from 'components/ad-ui-components/ad-icon/ad-icon.module';
import { NzRadioModule } from 'ng-zorro-antd/radio';

import { NzButtonModule } from 'ng-zorro-antd/button';
import { DeployService } from '../deploy/deploy.service';
import { CreateAgileDataAppModule } from 'pages/apps/create-agile-data-app/create-agile-data-app.module';
import { CreateNanaAssistantAppModule } from 'pages/apps/create-nana-assistant-app/create-nana-assistant-app.module';
import { AppApplicationComponent } from './app-application/app-application.component';
import { AppsModule } from 'pages/apps/apps.module';
import { TemplateListComponent } from './create-blank/template-list/template-list.component';
import { RookieTaskComponent } from './rookie-task/rookie-task.component';
import { TaskConstructModalComponent } from './rookie-task/task-construct-modal/task-construct-modal.component';
import { PipeModule } from 'common/pipe/pipe.module';
import { AdSelectModule } from 'components/ad-ui-components/ad-select/ad-select.module';
import { MySolutionsComponent } from './my-solutions/my-solutions.component';
import { NzTabsModule } from 'ng-zorro-antd/tabs';
import { TabContainerComponent } from './tab-container/tab-container.component';
import { AgGridModule } from 'ag-grid-angular';
import { NzTimelineModule } from 'ng-zorro-antd/timeline';
import { ProductNewsComponent } from './product-news/product-news.component';
import { SolutionCardModule } from 'components/bussiness-components/solution-card/solution-card.module';
import { SolutionBaseInfoFormModule } from 'components/bussiness-components/solution-base-info-form/solution-base-info-form.module';
import { NzDividerModule } from 'ng-zorro-antd/divider';
import { DeleteInfoModule } from 'components/bussiness-components/delete-info/delete-info.module';
import { AppInfoEditModule } from 'pages/app/app-info/app-info-edit/app-info-edit.module';
import { InputModule } from 'components/form-components/input/input.module';
import { NzFormModule } from 'ng-zorro-antd/form';
import { SolutionEntryCardModule } from 'components/bussiness-components/solution-entry-card/solution-entry-card.module';
import { SolutionTableModule } from 'components/bussiness-components/solution-table/solution-table.module';
import { RouterModule } from '@angular/router';
import { SolutionAntdTableModule } from 'components/bussiness-components/solution-antd-table/solution-antd-table.module';
import { ToolsSupportComponent } from './tools-support/tools-support.component';

@NgModule({
  imports: [
    CommonModule,
    RouterModule,
    HomeRoutingModule,
    NzSpinModule,
    CreateAppModule,
    NzCarouselModule,
    TranslateModule,
    NzToolTipModule,
    AdButtonModule,
    DirectiveModule,
    AdModalModule,
    AdEmptyModule,
    NzInputModule,
    NzTableModule,
    AdSelectModule,

    NzInputModule,
    FormsModule,
    ReactiveFormsModule,
    AdIconModule,
    NzRadioModule,
    CreateAgileDataAppModule,
    CreateNanaAssistantAppModule,
    NzButtonModule,
    AppsModule,
    AdIconModule,
    AdModalModule,
    PipeModule,
    AdModalModule,
    NzTabsModule,
    AgGridModule,
    NzTimelineModule,
    SolutionCardModule,
    SolutionBaseInfoFormModule,
    NzDividerModule,
    DeleteInfoModule,
    AppInfoEditModule,
    InputModule,
    NzFormModule,
    SolutionEntryCardModule,
    SolutionTableModule,
    SolutionAntdTableModule,
  ],
  declarations: [
    HomeComponent,
    CreateBlankComponent,
    CreateTemplateComponent,
    BeginnerTutorialComponent,
    CreateAIComponent,
    ProductBlockComponent,
    HomeAiCreateModalComponent,
    AISelectAppComponent,
    AppApplicationComponent,
    TemplateListComponent,
    RookieTaskComponent,
    TaskConstructModalComponent,
    MySolutionsComponent,
    TabContainerComponent,
    ProductNewsComponent,
    ToolsSupportComponent,
  ],
  exports: [],
  providers: [HomeService, DeployService],
})
export class HomeModule {}
