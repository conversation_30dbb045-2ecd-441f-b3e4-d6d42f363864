import { Component, OnInit, Inject } from '@angular/core';
import { ActivatedRoute, ParamMap, Router, convertToParamMap } from '@angular/router';
import { Observable, from, of } from 'rxjs';
import { map, mergeAll, reduce } from 'rxjs/operators';
import { AdModalService } from 'components/ad-ui-components/ad-modal/ad-modal.service';
import { AdAuthService } from '../service/auth.service';
import { SSO_LOGIN } from './sso.service';
import { HomeIntroService } from 'common/service/intro/home-intro/home-intro.service';
import { SystemConfigService } from 'common/service/system-config.service';
import { AdUserService } from '../service/user.service';
import { LocaleService, SUPPORT_LOCALE } from 'common/service/locale.service';
import { IndividualService } from 'pages/individual/individual.service';

export interface IDwSsoLogin {
  ssoLogin(queryParam: any): Observable<boolean>;
}

@Component({
  selector: 'app-sso-login',
  templateUrl: './sso-login.component.html',
  styleUrls: ['./sso-login.component.less'],
})
export class SsoLoginComponent implements OnInit {
  public showMessage = true; // 是否顯示登入訊息。完成登入即移除訊息
  private platformCategory;

  constructor(
    private router: Router,
    private activateRoute: ActivatedRoute,
    private languageService: LocaleService,
    private authService: AdAuthService,
    private dwModalService: AdModalService,
    @Inject(SSO_LOGIN) private issoLogins: IDwSsoLogin[],
    private configService: SystemConfigService,
    private userService: AdUserService,
    private individualService: IndividualService,
  ) {
    this.configService.getConfig().subscribe((config: any) => {
      this.platformCategory = config.platformCategory;
    });
  }

  /**
   * 使用 reduce 確保只取到一個結果值, 在 reduce 裡所操作的 Observable, 必定要 complete.
   * reduce 的參數，1: callback(), 2: 初始值，callback(acc: 初始值或上一次的結果值, val: eventLists的每1個值)，最後回傳一個新的狀態，再繼續執行.
   */
  ngOnInit(): void {
    // 取得路由參數
    this.activateRoute.queryParamMap.subscribe((params: ParamMap) => {
      const eventLists = from(this.issoLogins);
      const eventResults = eventLists.pipe(
        map((item) => {
          const ret = item.ssoLogin(params);
          return ret instanceof Observable ? ret : of(ret);
        }),
        mergeAll(),
        reduce((acc, val): boolean => acc || val, false), // 當有1個 true 結果就為 true 的判斷式.
      );

      eventResults.subscribe(
        (result: boolean) => {
          if (!result) {
            this.authService.logout();
          } else {
            HomeIntroService.initShowHomeIntro();
            const programId = params.get('dwProgramId');
            let routerLink = params.get('routerLink') || '';
            const dwLang = params.get('dwLang') || '';
            const newParams = this.filterParams(params);

            if (dwLang) {
              this.languageService.switchLocale(dwLang as SUPPORT_LOCALE);
            }

            if (this.platformCategory === 'TENANT') {
              this.userService.setUserInfo({ isTenantLogin: true }); // 是否是租户级登陆
              this.userService.setUserInfo({ isTenantActive: true }); // 租户级状态是否激活
            }

            // if (programId) {
            //   this.showMessage = false;
            //   this.dwProgramExecuteService.byId(programId, newParams);
            // } else {
            const qryString = this._getUrlQueryParams(newParams);
            // 導頁前往指定頁面時, 需帶其餘的 url query parameters.
            if (qryString) {
              routerLink = routerLink + '?' + qryString;
            }

            this.showMessage = false;
            this.loginedForwardUrl(routerLink);
            // }
          }
        },
        (error: any) => {
          let showMsg = false;

          if (error.hasOwnProperty('error') && error.error.hasOwnProperty('errorMessage')) {
            if (error.error.errorMessage) {
              showMsg = true;

              const ref = this.dwModalService.error(error.error.errorMessage);

              ref.afterClose.subscribe(() => {
                this.authService.logout();
              });
            }
          }

          if (!showMsg) {
            this.authService.logout();
          }
        },
      );
    });
  }

  /**
   * 將ParamMap中的專用key移除，產生一個新的ParamMap
   */
  private filterParams(paramMap: ParamMap): ParamMap {
    const keys = paramMap.keys;
    const params: { [key: string]: any } = {};
    let values: string[];
    keys.forEach((key: string): void => {
      switch (key) {
        case 'routerLink':
        case 'userToken':
        case 'dwLang':
          break;
        default:
          values = paramMap.getAll(key);
          if (values && values.length > 1) {
            params[key] = values;
          } else {
            params[key] = paramMap.get(key);
          }
      }
    });
    return convertToParamMap(params);
  }

  /**
   * [登入後]的要導頁的 url.
   * param {string} returnUrl: 導頁的 url.
   */
  public loginedForwardUrl(returnUrl: string): void {
    if (returnUrl !== null && returnUrl !== '' && returnUrl !== '/') {
      this.router.navigateByUrl(returnUrl);
    } else {
      if (this.individualService?.individualCase) {
        this.router.navigateByUrl('/individual/apps');
      } else {
        this.router.navigateByUrl('/');
      }
    }
  }

  /**
   * 取出網址列的 url query parameters
   * 須考慮?param1=user01&param1=user02&param1=user03
   * param {*} params
   * returns {string}
   */
  private _getUrlQueryParams(params: any): string {
    let qryString = '';
    const qryParams: { key: string; value: string }[] = [];
    let values: string[] = null;
    params.keys.forEach((key) => {
      values = params.getAll(key);
      values.forEach((value) => {
        qryParams.push({ key, value });
      });
    });

    if (qryParams.length === 0) {
      return qryString;
    }

    // 將其餘的 url query parameters 組成標準參數串.
    qryString = qryParams
      .map((param) => {
        return encodeURIComponent(param.key) + '=' + encodeURIComponent(param.value);
      })
      .join('&');

    return qryString;
  }
}
